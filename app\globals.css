/* Keep @import statements and @theme inline */

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
	/* ... keep all variable mappings (--color-background: var(--background); etc.) ... */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
	--color-sidebar-ring: var(--sidebar-ring);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar: var(--sidebar);
	--color-chart-5: var(--chart-5);
	--color-chart-4: var(--chart-4);
	--color-chart-3: var(--chart-3);
	--color-chart-2: var(--chart-2);
	--color-chart-1: var(--chart-1);
	--color-ring: var(--ring);
	--color-input: var(--input);
	--color-border: var(--border);
	--color-destructive: var(--destructive);
	--color-accent-foreground: var(--accent-foreground);
	--color-accent: var(--accent);
	--color-muted-foreground: var(--muted-foreground);
	--color-muted: var(--muted);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-secondary: var(--secondary);
	--color-primary-foreground: var(--primary-foreground);
	--color-primary: var(--primary);
	--color-popover-foreground: var(--popover-foreground);
	--color-popover: var(--popover);
	--color-card-foreground: var(--card-foreground);
	--color-card: var(--card);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}

	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

/* --- Light Mode Theme --- */
:root {
	--radius: 0.625rem; /* Keep your radius */

	/* Base */
	--background: oklch(1 0 0); /* White */
	--foreground: oklch(0.18 0.01 250); /* Dark Grey/Near Black */

	/* Cards & Popovers */
	--card: oklch(1 0 0); /* White */
	--card-foreground: oklch(0.18 0.01 250); /* Dark Grey/Near Black */
	--popover: oklch(1 0 0); /* White */
	--popover-foreground: oklch(0.18 0.01 250); /* Dark Grey/Near Black */

	/* Primary (Deep Orange/Gold) */
	--primary: oklch(0.646 0.14 77.5); /* ~ #C99700 */
	--primary-foreground: oklch(1 0 0); /* White for contrast */

	/* Secondary (Bright Yellow) */
	--secondary: oklch(0.857 0.188 90); /* ~ #FFD400 */
	--secondary-foreground: oklch(
		0.25 0.05 80
	); /* Dark Brown/Grey for contrast */

	/* Muted */
	--muted: oklch(0.96 0.01 250); /* Light Grey */
	--muted-foreground: oklch(0.55 0.02 250); /* Medium Grey */

	/* Accent (Using Secondary/Yellow for now, can be different) */
	--accent: oklch(0.98 0.05 90); /* Lighter Yellow */
	--accent-foreground: oklch(0.25 0.05 80); /* Dark Brown/Grey */

	/* Destructive (Keep your existing red/orange) */
	--destructive: oklch(0.577 0.245 27.325);

	/* Borders and Inputs */
	--border: oklch(0.92 0.015 250); /* Slightly darker grey */
	--input: oklch(0.92 0.015 250); /* Same as border */
	--ring: oklch(
		0.646 0.14 77.5 / 50%
	); /* Primary color at 50% opacity for focus ring */

	/* Charts (Keep existing or redefine with brand colors) */
	--chart-1: oklch(0.646 0.14 77.5); /* Primary */
	--chart-2: oklch(0.857 0.188 90); /* Secondary */
	--chart-3: oklch(0.6 0.118 184.704); /* Your Teal */
	--chart-4: oklch(0.398 0.07 227.392); /* Your Blue */
	--chart-5: oklch(0.769 0.188 70.08); /* Your Green */

	/* Sidebar (Example using muted/primary) */
	--sidebar: oklch(0.98 0.005 250); /* Very Light Grey */
	--sidebar-foreground: oklch(0.3 0.01 250); /* Darker Grey */
	--sidebar-primary: oklch(0.646 0.14 77.5); /* Primary */
	--sidebar-primary-foreground: oklch(1 0 0); /* White */
	--sidebar-accent: oklch(0.96 0.01 250); /* Light Grey (same as muted) */
	--sidebar-accent-foreground: oklch(0.18 0.01 250); /* Dark Grey */
	--sidebar-border: oklch(0.92 0.015 250); /* Border */
	--sidebar-ring: oklch(0.646 0.14 77.5 / 50%); /* Ring */
}

/* --- Dark Mode Theme --- */
.dark {
	/* Base */
	--background: oklch(0.18 0.01 250); /* Dark Grey/Near Black */
	--foreground: oklch(0.97 0.005 250); /* Very Light Grey/Near White */

	/* Cards & Popovers */
	--card: oklch(0.22 0.015 250); /* Slightly lighter dark grey */
	--card-foreground: oklch(0.97 0.005 250); /* Near White */
	--popover: oklch(0.22 0.015 250); /* Slightly lighter dark grey */
	--popover-foreground: oklch(0.97 0.005 250); /* Near White */

	/* Primary (Adjusted Orange/Gold for Dark Mode) */
	--primary: oklch(0.7 0.15 77.5); /* Lighter/Brighter Orange */
	--primary-foreground: oklch(0.15 0.02 70); /* Dark Brown/Black for contrast */

	/* Secondary (Adjusted Yellow for Dark Mode) */
	--secondary: oklch(0.8 0.16 90); /* Slightly desaturated/darker Yellow */
	--secondary-foreground: oklch(
		0.15 0.02 70
	); /* Dark Brown/Black for contrast */

	/* Muted */
	--muted: oklch(0.25 0.02 250); /* Darker Grey */
	--muted-foreground: oklch(0.65 0.02 250); /* Lighter Grey */

	/* Accent (Using Adjusted Secondary/Yellow) */
	--accent: oklch(0.3 0.06 90); /* Darker Yellow/Mustard */
	--accent-foreground: oklch(0.97 0.005 250); /* Near White */

	/* Destructive (Adjusted Red/Orange for Dark Mode) */
	--destructive: oklch(0.65 0.25 27.325); /* Brighter Destructive */
	--destructive-foreground: oklch(1 0 0); /* White Text */

	/* Borders and Inputs */
	--border: oklch(0.3 0.02 250 / 80%); /* Semi-transparent darker grey */
	--input: oklch(0.3 0.02 250 / 90%); /* Slightly less transparent */
	--ring: oklch(0.7 0.15 77.5 / 50%); /* Brighter Primary at 50% opacity */

	/* Charts (Adjust lightness/chroma for dark mode) */
	--chart-1: oklch(0.7 0.15 77.5); /* Primary */
	--chart-2: oklch(0.8 0.16 90); /* Secondary */
	--chart-3: oklch(0.65 0.12 184.704); /* Teal */
	--chart-4: oklch(0.5 0.08 227.392); /* Blue */
	--chart-5: oklch(0.8 0.19 70.08); /* Green */

	/* Sidebar (Example using adjusted colors) */
	--sidebar: oklch(0.22 0.015 250); /* Dark Card Bg */
	--sidebar-foreground: oklch(0.85 0.01 250); /* Lighter Grey */
	--sidebar-primary: oklch(0.7 0.15 77.5); /* Primary */
	--sidebar-primary-foreground: oklch(0.15 0.02 70); /* Dark Text */
	--sidebar-accent: oklch(0.3 0.06 90); /* Darker Yellow */
	--sidebar-accent-foreground: oklch(0.97 0.005 250); /* Near White */
	--sidebar-border: oklch(0.3 0.02 250 / 80%); /* Border */
	--sidebar-ring: oklch(0.7 0.15 77.5 / 50%); /* Ring */
}

@layer base {
	* {
		/* Apply border using the CSS variable */
		@apply border-border outline-ring/50;
	}
	body {
		/* Apply background and text colors using CSS variables */
		@apply bg-background text-foreground;
		/* Add font-smoothing for better text rendering */
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		/* Increase base font size for better readability */
		font-size: 18px;
		line-height: 1.6;
	}

	/* Increase font sizes globally for better readability */
	h1 {
		@apply text-5xl md:text-6xl;
		line-height: 1.2;
	}
	h2 {
		@apply text-4xl md:text-5xl;
		line-height: 1.3;
	}
	h3 {
		@apply text-3xl md:text-4xl;
		line-height: 1.3;
	}
	h4 {
		@apply text-2xl md:text-3xl;
		line-height: 1.4;
	}
	h5 {
		@apply text-xl md:text-2xl;
		line-height: 1.4;
	}
	h6 {
		@apply text-lg md:text-xl;
		line-height: 1.4;
	}
	p {
		@apply text-lg md:text-xl;
		line-height: 1.6;
	}
	/* Increase button text size */
	button {
		@apply text-lg md:text-xl;
		line-height: 1.4;
	}
	/* Increase input text size */
	input, textarea, select {
		@apply text-lg md:text-xl;
		line-height: 1.4;
	}
	/* Increase navigation text size */
	nav a {
		@apply text-lg md:text-xl;
		line-height: 1.4;
	}
	/* Increase card and component text sizes */
	.card p, .card-content p {
		@apply text-lg md:text-xl;
	}
	/* Increase table text size */
	table td, table th {
		@apply text-lg md:text-xl;
	}
	/* Increase list text size */
	li {
		@apply text-lg md:text-xl;
		line-height: 1.6;
	}
	/* Increase label text size */
	label {
		@apply text-lg md:text-xl;
	}
}

/* globals.css */

::-webkit-scrollbar {
	width: 2px; /* Thin scrollbar */
	height: 2px; /* Thin horizontal scrollbar */
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: var(--primary); /* Use CSS variable for dynamic theming */
	border-radius: 9999px;
	border: 1px solid transparent;
	background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
	background-color: var(--primary-hover, #1e7e34); /* Optional darker hover */
}

/* Firefox support */
* {
	scrollbar-width: thin;
	scrollbar-color: var(--primary) transparent;
}

@keyframes scanLine {
	0% {
		transform: translateY(-50px);
		opacity: 0.5;
	}
	50% {
		transform: translateY(50px);
		opacity: 1;
	}
	100% {
		transform: translateY(-50px);
		opacity: 0.5;
	}
}
.animate-scan-line {
	animation: scanLine 2.5s ease-in-out infinite;
}

@keyframes glow {
	0%,
	100% {
		box-shadow: 0 0 3px #ffd700;
	}
	50% {
		box-shadow: 0 0 6px #ffd700;
	}
}

.glow-border {
	animation: glow 1.5s ease-in-out infinite;
}
