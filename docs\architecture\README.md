# 🏗️ Architecture Documentation

This folder contains documentation related to the architecture of the SmartEdu platform.

## Contents

- [System Architecture](./system-architecture.md) - Overview of the system architecture
- [Application Flow](./flow.md) - Overview of the application flow and user journeys

## Overview

The SmartEdu platform is built with a modern architecture using Next.js, React, TypeScript, and Redux. This folder contains documentation for the overall system architecture and application flow.

## Key Concepts

- **Frontend Architecture**: The SmartEdu frontend is built with Next.js, React, TypeScript, and Redux.
- **Backend Integration**: The frontend integrates with a backend API built with Node.js, Express, and MongoDB.
- **State Management**: The application uses Redux for state management, with a focus on type safety and performance.
- **Routing**: Next.js is used for routing, with a focus on server-side rendering and static site generation.

## Related Documentation

- [API Integration Documentation](../api-integration/README.md)
- [Development Documentation](../development/README.md)
- [Testing Documentation](../testing/README.md)
