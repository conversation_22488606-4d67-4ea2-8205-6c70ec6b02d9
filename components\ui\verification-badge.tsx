"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { BadgeCheck } from "lucide-react";

interface VerificationBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The size of the badge
   * @default "sm"
   */
  size?: "xs" | "sm" | "md" | "lg";

  /**
   * The color of the badge
   * @default "gold"
   */
  color?: "gold" | "blue" | "primary";

  /**
   * Whether to show the tooltip
   * @default true
   */
  showTooltip?: boolean;

  /**
   * The tooltip text
   * @default "Verified Active User"
   */
  tooltipText?: string;
}

/**
 * A verification badge component that shows a checkmark in a circle
 * Used to indicate verified or active users
 */
export function VerificationBadge({
  size = "sm",
  color = "gold",
  showTooltip = true,
  tooltipText = "Verified Active User",
  className,
  ...props
}: VerificationBadgeProps) {
  // Size classes
  const sizeClasses = {
    xs: "h-3 w-3 text-[0.5rem]",
    sm: "h-4 w-4 text-[0.6rem]",
    md: "h-5 w-5 text-[0.7rem]",
    lg: "h-6 w-6 text-[0.8rem]",
  };

  // Color classes
  const colorClasses = {
    gold: "bg-amber-500 text-white border-amber-600",
    blue: "bg-blue-500 text-white border-blue-600",
    primary: "bg-primary text-primary-foreground border-primary-600",
  };


  const FilledBadgeCheck = ({
    className,
    size = 24,
  }: {
    className?: string;
    size?: number;
  }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 256 256"
      className={cn("fill-current", className)} // tailwind color control here
    >
      <path d="M225.86,102.82c-3.77-3.94-7.67-8-9.14-11.57-1.36-3.27-1.44-8.69-1.52-13.94-.15-9.76-.31-20.82-8-28.51s-18.75-7.85-28.51-8c-5.25-.08-10.67-.16-13.94-1.52-3.56-1.47-7.63-5.37-11.57-9.14C146.28,23.51,138.44,16,128,16s-18.27,7.51-25.18,14.14c-3.94,3.77-8,7.67-11.57,9.14C88,40.64,82.56,40.72,77.31,40.8c-9.76.15-20.82.31-28.51,8S41,67.55,40.8,77.31c-.08,5.25-.16,10.67-1.52,13.94-1.47,3.56-5.37,7.63-9.14,11.57C23.51,109.72,16,117.56,16,128s7.51,18.27,14.14,25.18c3.77,3.94,7.67,8,9.14,11.57,1.36,3.27,1.44,8.69,1.52,13.94.15,9.76.31,20.82,8,28.51s18.75,7.85,28.51,8c5.25.08,10.67.16,13.94,1.52,3.56,1.47,7.63,5.37,11.57,9.14C109.72,232.49,117.56,240,128,240s18.27-7.51,25.18-14.14c3.94-3.77,8-7.67,11.57-9.14,3.27-1.36,8.69-1.44,13.94-1.52,9.76-.15,20.82-.31,28.51-8s7.85-18.75,8-28.51c.08-5.25.16-10.67,1.52-13.94,1.47-3.56,5.37-7.63,9.14-11.57C232.49,146.28,240,138.44,240,128S232.49,109.73,225.86,102.82Zm-52.2,6.84-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35a8,8,0,0,1,11.32,11.32Z" />
    </svg>
  );

  const badge = (
    <div className={cn("flex items-center justify-center", className)} {...props}>
      <FilledBadgeCheck
        size={size === "xs" ? 14 : size === "sm" ? 16 : size === "md" ? 20 : 24}
        className={cn(
          color === "gold" ? "text-amber-500" :
            color === "blue" ? "text-blue-500" :
              "text-primary"
        )}
      />
    </div>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          {badge}
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
