'use client'

import React from 'react'
import Image from 'next/image'

interface SleekLogoLoaderProps {
  className?: string
}

export function SleekLogoLoader({
  className = ""
}: SleekLogoLoaderProps) {
  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center bg-background ${className}`}
      role="status"
      aria-label="Loading application"
    >
      {/* Just the Logo with Tailwind animations */}
      <div className="w-32 h-32 animate-pulse transition-all duration-300 ease-[cubic-bezier(0.25, 0.1, 0.25, 1)]">
        <Image
          src="/icon.png"
          alt="1Tech Academy"
          width={128}
          height={128}
          className="w-full h-full object-contain"
          priority
        />
      </div>
    </div>
  )
}

