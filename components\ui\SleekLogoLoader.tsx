'use client'

import React from 'react'
import Image from 'next/image'

interface SleekLogoLoaderProps {
  className?: string
}

export function SleekLogoLoader({
  className = ""
}: SleekLogoLoaderProps) {
  return (
    <>
      {/* Global Keyframes */}
      <style jsx global>{`
        @keyframes logo-grow {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes logo-pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
        }

        @keyframes logo-exit {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          100% {
            transform: scale(0);
            opacity: 0;
          }
        }
      `}</style>

      <div
        className={`fixed inset-0 z-50 flex items-center justify-center bg-background ${className}`}
        role="status"
        aria-label="Loading application"
      >
        {/* Just the Logo */}
        <div className="logo-icon w-32 h-32">
          <Image
            src="/icon.png"
            alt="1Tech Academy"
            width={128}
            height={128}
            className="w-full h-full object-contain"
            priority
          />
        </div>
      </div>

      {/* Animation Sequence */}
      <style jsx>{`
        .logo-icon {
          animation:
            logo-grow 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) forwards,
            logo-pulse 2s cubic-bezier(0.25, 0.1, 0.25, 1) infinite 0.6s,
            logo-exit 0.4s cubic-bezier(0.25, 0.1, 0.25, 1) forwards 3s;
        }
      `}</style>
    </>
  )
}

