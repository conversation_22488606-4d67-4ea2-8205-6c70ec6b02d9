'use client'

import React from 'react'
import Image from 'next/image'

interface SleekLogoLoaderProps {
  /** The path to your logo image (e.g., "/logo.png" or "/logo.svg") */
  logoSrc?: string
  /** Alt text for the logo image */
  logoAlt?: string
  /** Desired size for the logo (width and height in pixels) */
  logoSize?: number
  /** Optional additional CSS classes for the main container */
  className?: string
}

export function SleekLogoLoader({
  logoSrc = "/icon.png", // Default, ensure this path is correct for your project
  logoAlt = "Loading Logo",
  logoSize = 128,       // You can adjust this default size
  className = ""
}: SleekLogoLoaderProps) {
  return (
    <>
      {/*
        This <style jsx global> block defines the keyframes globally.
        Keyframes need to be global in CSS to be referenced by animation properties.
        <style jsx> hashes class names but not keyframe names directly,
        so defining them globally is the standard way.
      */}
      <style jsx global>{`
        @keyframes initial-logo-appear {
          0% {
            opacity: 0;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes logo-pulse {
          0%,
          100% {
            transform: scale(1);
            opacity: 0.95;
          }
          50% {
            transform: scale(1.04);
            opacity: 1;
          }
        }
      `}</style>

      <div
        className={`flex h-screen w-screen items-center justify-center bg-black ${className}`}
        role="status"
        aria-label="Loading application"
      >
        {/*
          The 'animated-logo-container' class defined in the <style jsx> below
          will apply our custom animations.
        */}
        <div
          className="animated-logo-container"
          style={{
            width: logoSize,
            height: logoSize,
          }}
        >
          <Image
            src={logoSrc}
            alt={logoAlt}
            width={logoSize}
            height={logoSize}
            className="object-contain w-full h-full" // Make image fill parent
            priority
            unoptimized={logoSrc.endsWith('.svg')}
          />
        </div>
      </div>

      {/*
        This <style jsx> block defines styles scoped to this component.
        The 'animated-logo-container' class will be hashed by styled-jsx
        to prevent naming conflicts.
      */}
      <style jsx>{`
        .animated-logo-container {
          /*
            Apply both animations:
            1. 'initial-logo-appear': Runs for 0.7s, eases out, and holds its final state.
            2. 'logo-pulse': Starts after a 0.7s delay (to wait for the appear animation),
                             runs for 2.5s per cycle, eases in and out, and repeats infinitely.
          */
          animation: initial-logo-appear 0.7s ease-out forwards,
                     logo-pulse 2.5s ease-in-out 0.7s infinite;
        }
      `}</style>
    </>
  )
}

// Example Usage (in a page or layout, e.g., loading.tsx):
// import { SleekLogoLoader } from './path-to-SleekLogoLoader';
//
// export default function LoadingPage() {
//   return <SleekLogoLoader logoSrc="/my-app-logo.svg" logoSize={150} />;
// }