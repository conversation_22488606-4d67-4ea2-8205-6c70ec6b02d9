'use client'

import React from 'react'
import Image from 'next/image'

interface SleekLogoLoaderProps {
  message?: string
  className?: string
}

export function SleekLogoLoader({
  message = "Loading...",
  className = ""
}: SleekLogoLoaderProps) {
  return (
    <>
      {/* Global Keyframes */}
      <style jsx global>{`
        @keyframes sleek-logo-appear {
          0% {
            opacity: 0;
            transform: scale(0.8) translateY(10px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }

        @keyframes sleek-logo-pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.9;
          }
          50% {
            transform: scale(1.08);
            opacity: 1;
          }
        }

        @keyframes sleek-ring-pulse-1 {
          0%, 100% {
            transform: scale(1);
            opacity: 0.3;
          }
          50% {
            transform: scale(1.15);
            opacity: 0.6;
          }
        }

        @keyframes sleek-ring-pulse-2 {
          0%, 100% {
            transform: scale(1);
            opacity: 0.2;
          }
          50% {
            transform: scale(1.25);
            opacity: 0.4;
          }
        }

        @keyframes sleek-text-fade {
          0% {
            opacity: 0;
            transform: translateY(15px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes sleek-dots {
          0%, 80%, 100% {
            transform: scale(1);
            opacity: 0.4;
          }
          40% {
            transform: scale(1.2);
            opacity: 1;
          }
        }
      `}</style>

      <div
        className={`fixed inset-0 z-50 flex flex-col items-center justify-center bg-gradient-to-br from-background via-background to-muted/10 ${className}`}
        role="status"
        aria-label="Loading application"
      >
        {/* Logo Container with Pulsating Rings */}
        <div className="logo-container relative mb-8">
          {/* Pulsating Background Rings */}
          <div className="ring-1 absolute inset-0 w-28 h-28 rounded-full bg-primary/20" />
          <div className="ring-2 absolute inset-0 w-28 h-28 rounded-full bg-primary/15" />

          {/* Logo */}
          <div className="logo-wrapper relative z-10 w-28 h-28 rounded-full bg-white/95 p-4 shadow-xl">
            <Image
              src="/icon.png"
              alt="1Tech Academy"
              width={112}
              height={112}
              className="w-full h-full object-contain"
              priority
            />
          </div>
        </div>

        {/* Brand Text */}
        <div className="brand-text text-center space-y-3 mb-6">
          <h1 className="text-3xl font-bold text-foreground">
            1Tech Academy
          </h1>
          <p className="text-xl text-primary/80 font-pacifico">
            Beyond Limits, Beyond Today
          </p>
        </div>

        {/* Loading Message and Dots */}
        <div className="loading-section text-center">
          <p className="loading-message text-sm text-muted-foreground mb-4">{message}</p>

          {/* Loading Dots */}
          <div className="flex justify-center space-x-2">
            <div className="dot-1 w-2.5 h-2.5 bg-primary/70 rounded-full" />
            <div className="dot-2 w-2.5 h-2.5 bg-primary/70 rounded-full" />
            <div className="dot-3 w-2.5 h-2.5 bg-primary/70 rounded-full" />
          </div>
        </div>
      </div>

      {/* Scoped Styles with Easier Bezier Curves */}
      <style jsx>{`
        .logo-container {
          animation: sleek-logo-appear 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
        }

        .logo-wrapper {
          animation: sleek-logo-pulse 3s cubic-bezier(0.25, 0.1, 0.25, 1) infinite 0.8s;
        }

        .ring-1 {
          animation: sleek-ring-pulse-1 2.5s cubic-bezier(0.25, 0.1, 0.25, 1) infinite 0.5s;
        }

        .ring-2 {
          animation: sleek-ring-pulse-2 3s cubic-bezier(0.25, 0.1, 0.25, 1) infinite 0.7s;
        }

        .brand-text {
          animation: sleek-text-fade 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
          animation-delay: 1s;
          opacity: 0;
        }

        .loading-section {
          animation: sleek-text-fade 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
          animation-delay: 1.3s;
          opacity: 0;
        }

        .dot-1 {
          animation: sleek-dots 1.5s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
          animation-delay: 1.5s;
        }

        .dot-2 {
          animation: sleek-dots 1.5s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
          animation-delay: 1.7s;
        }

        .dot-3 {
          animation: sleek-dots 1.5s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
          animation-delay: 1.9s;
        }
      `}</style>
    </>
  )
}

