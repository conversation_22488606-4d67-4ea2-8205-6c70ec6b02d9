'use client'

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { Loader2, Shield } from 'lucide-react'

interface AppInitializationLoaderProps {
  message?: string
  className?: string
}

export function AppInitializationLoader({
  message = "Initializing...",
  className = ""
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 ${className}`}>
      <div className="flex items-center space-x-8 max-w-4xl mx-auto px-6">
        {/* Logo Section - Left Side */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          {/* Pulsating background rings */}
          <motion.div
            className="absolute inset-0 w-32 h-32 rounded-full bg-primary/10"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute inset-0 w-32 h-32 rounded-full bg-primary/20"
            animate={{
              scale: [1, 1.4, 1],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.3
            }}
          />

          {/* Logo Image */}
          <motion.div
            className="relative z-10 w-32 h-32 rounded-full overflow-hidden bg-white/90 p-2 shadow-lg"
            animate={{
              scale: [1, 1.05, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Image
              src="/icon.png"
              alt="1Tech Academy Logo"
              width={128}
              height={128}
              className="w-full h-full object-contain"
              priority
            />
          </motion.div>
        </motion.div>

        {/* Brand Text Section - Right Side */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          className="space-y-4"
        >
          {/* Company Name */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-4xl md:text-5xl font-bold text-foreground tracking-wide"
          >
            1Tech Academy
          </motion.h1>

          {/* Slogan in Pacifico Italics */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="text-xl md:text-2xl text-primary/80 font-pacifico"
          >
            Beyond Limits, Beyond Today
          </motion.p>
        </motion.div>
      </div>

      {/* Subtle Loading Indicator - Bottom Center */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.2 }}
        className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
      >
        {/* Progress Dots */}
        <div className="flex justify-center space-x-3">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-3 h-3 bg-primary/60 rounded-full"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.4, 1, 0.4],
              }}
              transition={{
                duration: 1.8,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Loading Message */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.5 }}
          className="text-sm text-muted-foreground mt-4 text-center"
        >
          {message}
        </motion.p>
      </motion.div>
    </div>
  )
}

// Alternative minimal version
export function MinimalAppLoader({
  message = "Loading...",
  className = ""
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center ${className}`}>
      <div className="text-center space-y-4">
        <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

// Premium version with more animations
export function PremiumAppLoader({
  message = "Initializing...",
  className = ""
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center bg-gradient-to-br from-primary/5 via-background to-secondary/5 ${className}`}>
      <div className="text-center space-y-8 max-w-lg mx-auto px-6">
        {/* Animated Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          <motion.div
            className="w-24 h-24 mx-auto rounded-full bg-gradient-to-r from-primary to-secondary p-1"
            animate={{ rotate: 360 }}
            transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
          >
            <div className="w-full h-full rounded-full bg-background flex items-center justify-center">
              <Shield className="w-10 h-10 text-primary" />
            </div>
          </motion.div>

          {/* Pulsing rings */}
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="absolute inset-0 rounded-full border border-primary/20"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 0, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.4,
                ease: "easeInOut"
              }}
            />
          ))}
        </motion.div>

        {/* Brand */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="space-y-2"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            1Tech Academy
          </h1>
          <p className="text-muted-foreground">
            Empowering Africa's Tech Leaders
          </p>
        </motion.div>

        {/* Loading Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="space-y-4"
        >
          <p className="text-lg font-medium text-foreground">{message}</p>

          {/* Progress bar */}
          <div className="w-64 h-1 bg-muted rounded-full mx-auto overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-primary to-secondary"
              animate={{ x: ["-100%", "100%"] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      </div>
    </div>
  )
}
