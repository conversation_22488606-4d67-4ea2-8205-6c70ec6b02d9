'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Loader2, Shield, Zap } from 'lucide-react'

interface AppInitializationLoaderProps {
  message?: string
  className?: string
}

export function AppInitializationLoader({ 
  message = "Initializing...", 
  className = "" 
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 ${className}`}>
      <div className="text-center space-y-8 max-w-md mx-auto px-6">
        {/* Logo/Brand Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="space-y-4"
        >
          <div className="relative mx-auto w-20 h-20 mb-6">
            {/* Animated background circle */}
            <motion.div
              className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-primary/40"
              animate={{ 
                scale: [1, 1.1, 1],
                opacity: [0.5, 0.8, 0.5]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            
            {/* Main icon */}
            <div className="relative z-10 w-full h-full rounded-full bg-primary/10 flex items-center justify-center">
              <Shield className="w-8 h-8 text-primary" />
            </div>
          </div>

          <h1 className="text-2xl font-bold text-foreground">
            1Tech Academy
          </h1>
          <p className="text-sm text-muted-foreground">
            Empowering Africa's Tech Leaders
          </p>
        </motion.div>

        {/* Loading Animation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-6"
        >
          {/* Modern Spinner */}
          <div className="relative mx-auto w-12 h-12">
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-primary/20"
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-transparent border-t-primary border-r-primary"
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <Zap className="w-5 h-5 text-primary" />
            </div>
          </div>

          {/* Loading Message */}
          <div className="space-y-2">
            <p className="text-lg font-medium text-foreground">
              {message}
            </p>
            <p className="text-sm text-muted-foreground">
              Setting up your learning environment...
            </p>
          </div>

          {/* Progress Dots */}
          <div className="flex justify-center space-x-2">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-primary/60 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-xs text-muted-foreground space-y-1"
        >
          <p>Securing your session...</p>
          <p>Loading your dashboard...</p>
        </motion.div>
      </div>
    </div>
  )
}

// Alternative minimal version
export function MinimalAppLoader({ 
  message = "Loading...", 
  className = "" 
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center ${className}`}>
      <div className="text-center space-y-4">
        <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

// Premium version with more animations
export function PremiumAppLoader({ 
  message = "Initializing...", 
  className = "" 
}: AppInitializationLoaderProps) {
  return (
    <div className={`flex h-screen w-screen items-center justify-center bg-gradient-to-br from-primary/5 via-background to-secondary/5 ${className}`}>
      <div className="text-center space-y-8 max-w-lg mx-auto px-6">
        {/* Animated Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          <motion.div
            className="w-24 h-24 mx-auto rounded-full bg-gradient-to-r from-primary to-secondary p-1"
            animate={{ rotate: 360 }}
            transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
          >
            <div className="w-full h-full rounded-full bg-background flex items-center justify-center">
              <Shield className="w-10 h-10 text-primary" />
            </div>
          </motion.div>
          
          {/* Pulsing rings */}
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="absolute inset-0 rounded-full border border-primary/20"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 0, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.4,
                ease: "easeInOut"
              }}
            />
          ))}
        </motion.div>

        {/* Brand */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="space-y-2"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            1Tech Academy
          </h1>
          <p className="text-muted-foreground">
            Empowering Africa's Tech Leaders
          </p>
        </motion.div>

        {/* Loading Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="space-y-4"
        >
          <p className="text-lg font-medium text-foreground">{message}</p>
          
          {/* Progress bar */}
          <div className="w-64 h-1 bg-muted rounded-full mx-auto overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-primary to-secondary"
              animate={{ x: ["-100%", "100%"] }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      </div>
    </div>
  )
}
