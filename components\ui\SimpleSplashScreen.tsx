'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface SimpleSplashScreenProps {
  onComplete?: () => void
  className?: string
}

export function SimpleSplashScreen({ onComplete, className }: SimpleSplashScreenProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [videoLoaded, setVideoLoaded] = useState(false)
  const [showFallback, setShowFallback] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle video events and fallback
  useEffect(() => {
    // Set a timeout to show fallback if video doesn't load within 2 seconds
    timeoutRef.current = setTimeout(() => {
      console.log('Video loading timeout, showing fallback')
      setShowFallback(true)
    }, 2000)

    const video = videoRef.current
    if (!video) {
      setShowFallback(true)
      return
    }

    const handleLoadedData = () => {
      console.log('Video loaded successfully')
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      setVideoLoaded(true)
      setShowFallback(false)
      video.play().catch((error) => {
        console.error('Video play failed:', error)
        setShowFallback(true)
      })
    }

    const handleEnded = () => {
      setIsVisible(false)
      setTimeout(() => onComplete?.(), 500) // Wait for fade out
    }

    const handleError = (error: Event) => {
      console.warn('Video failed to load:', error)
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      setShowFallback(true)
    }

    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
    }
  }, [onComplete])

  // Auto-complete fallback after 3 seconds
  useEffect(() => {
    if (showFallback) {
      const fallbackTimeout = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => onComplete?.(), 500)
      }, 3000)

      return () => clearTimeout(fallbackTimeout)
    }
  }, [showFallback, onComplete])

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 z-50 flex items-center justify-center bg-white ${className || ''}`}
    >
      <div className="flex items-center justify-center w-full h-full">
        {/* Show video if loaded and not showing fallback */}
        {videoLoaded && !showFallback && (
          <video
            ref={videoRef}
            className="w-full h-full object-contain"
            muted
            playsInline
            preload="auto"
          >
            <source src="/animations/splash-video.mp4" type="video/mp4" />
            <source src="/animations/splash-video.webm" type="video/webm" />
          </video>
        )}

        {/* Hidden video for loading detection */}
        {!videoLoaded && (
          <video
            ref={videoRef}
            className="hidden"
            muted
            playsInline
            preload="auto"
          >
            <source src="/animations/splash-video.mp4" type="video/mp4" />
            <source src="/animations/splash-video.webm" type="video/webm" />
          </video>
        )}

        {/* Fallback content */}
        {(showFallback || !videoLoaded) && (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="mb-6"
              >
                <h1 className="text-4xl md:text-6xl font-bold text-gray-800 tracking-wide">
                  1Tech Academy
                </h1>
                <p className="text-lg md:text-xl text-gray-600 mt-4 font-light">
                  Empowering Africa's Tech Leaders
                </p>
              </motion.div>

              {/* Loading animation */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex justify-center space-x-2"
              >
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-3 h-3 bg-blue-600 rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </motion.div>
            </div>
          </div>
        )}

        {/* Skip Button */}
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
          onClick={() => {
            setIsVisible(false)
            setTimeout(() => onComplete?.(), 300)
          }}
          className="absolute top-8 right-8 text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200 bg-white/80 px-3 py-1 rounded-full"
        >
          Skip
        </motion.button>
      </div>
    </motion.div>
  )
}
