"use client";

import { useState, useEffect } from "react";

export function useMobile(): boolean {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		// Function to check if the window width is mobile
		const checkMobile = () => {
			setIsMobile(window.innerWidth < 768); // Consider < 768px as mobile
		};

		// Check on initial load
		checkMobile();

		// Add event listener for window resize
		window.addEventListener("resize", checkMobile);

		// Clean up event listener
		return () => window.removeEventListener("resize", checkMobile);
	}, []);

	return isMobile;
}
